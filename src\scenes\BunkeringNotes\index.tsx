import { WrappedFormUtils } from "@ant-design/compatible/lib/form/Form";
import { SettingOutlined } from "@ant-design/icons";
import {
	But<PERSON>,
	Card,
	Col,
	DatePicker,
	Dropdown,
	Menu,
	Modal,
	Pagination,
	Row,
	Table,
} from "antd";
import MenuItem from "antd/lib/menu/MenuItem";
import { ColumnProps } from "antd/lib/table";
import {
	FilterDropdownProps,
	SorterResult,
	TablePaginationConfig,
} from "antd/lib/table/interface";
import { inject, observer } from "mobx-react";
import moment from "moment";
import { Document, Page, pdfjs } from "react-pdf";
import AppComponentBase from "../../components/AppComponentBase";
import Chat from "../../components/Chat";
import FilterSelect from "../../components/FilterSelect/FilterSelect";
import { L, isGranted } from "../../lib/abpUtility";
import { FilterByColumn } from "../../models/Sort/SortState";
import { GetBunkeringNotesOutput } from "../../services/bunkeringNotes/dto/GetBunkeringNotesOutput";
import { EntityDto } from "../../services/dto/entityDto";
import BunkeringNotesStore from "../../stores/bunkeringNotesStore";
import FuelTypeStore from "../../stores/fuelTypeStore";
import PortStore from "../../stores/portStore";
import ShipStore from "../../stores/shipStore";
import Stores from "../../stores/storeIdentifier";
import utils from "../../utils/utils";
import { ModalType } from "../ModalConsts";
import {
	getTablePaginationOptions,
	renderDate,
	renderFilterIcon,
} from "../renderUtils";
import UpdateBunkeringNotes from "./components/updateBunkeringNotes";
import "./index.less";
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

export interface IBunkeringNotesProps {
	bunkeringNotesStore: BunkeringNotesStore;
	fuelTypeStore: FuelTypeStore;
	portStore: PortStore;
	shipStore: ShipStore;
}

interface IBunkeringNotesDateFilters {
	bunkeringDateStart?: string;
	bunkeringDateEnd?: string;
}

export interface IBunkeringNotesState {
	shipId: number;
	maxResultCount: number;
	pageNumber: number;
	skipCount: number;
	isPdfModalVisible: boolean;
	fetchingFilters: boolean;
	selectedPdfRecord: GetBunkeringNotesOutput | null;
	id: number;
	sorters: SorterResult<GetBunkeringNotesOutput>[];
	modalVisible: boolean;
	okButtonDisabled: boolean;
	loading: boolean;
	tempSearchValue: string;
	filters: Array<FilterByColumn>;
	activeDateFilters: IBunkeringNotesDateFilters;
}

const confirm = Modal.confirm;

type PreviousState = {
	sorters: IBunkeringNotesState["sorters"];
	filters: IBunkeringNotesState["filters"];
	activeDateFilters: IBunkeringNotesState["activeDateFilters"];
};

@inject(Stores.BunkeringNotesStore)
@inject(Stores.FuelTypeStore)
@inject(Stores.PortStore)
@inject(Stores.ShipStore)
@observer
class BunkeringNotes extends AppComponentBase<
	IBunkeringNotesProps,
	IBunkeringNotesState
> {
	formRef?: WrappedFormUtils;
	state: IBunkeringNotesState = {
		maxResultCount: 10,
		skipCount: 0,
		shipId: 0,
		sorters: [],
		pageNumber: 1,
		isPdfModalVisible: false,
		fetchingFilters: false,
		selectedPdfRecord: null,
		id: 0,
		modalVisible: false,
		okButtonDisabled: false,
		filters: [],
		loading: false,
		tempSearchValue: "",
		activeDateFilters: {
			bunkeringDateStart: undefined,
			bunkeringDateEnd: undefined,
		},
	};

	async componentDidMount() {
		const prevState = this.extractPreviousState();

		this.setState({ ...prevState }, async () => {
			await this.getAll();
		});
	}

	extractPreviousState(): PreviousState {
		let state: PreviousState = {
			sorters: [],
			filters: [],
			activeDateFilters: {},
		};

		const prevState =
			utils.getSortAndFilterFromStorage<PreviousState>("docs-filters");

		if (prevState)
			state = {
				...prevState,
			};

		utils.removeStateFromStorage("docs-filters");

		return state;
	}

	componentWillUnmount(): void {
		const settings: PreviousState = {
			sorters: this.state.sorters,
			filters: this.state.filters,
			activeDateFilters: this.state.activeDateFilters,
		};

		utils.saveSortAndFilterToStorage("docs-filters", settings);
	}
	async getAll() {
		const sortString = utils.getSorterString(this.state.sorters);
		this.setState({ loading: true });

		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		await this.props.bunkeringNotesStore.getAll({
			maxResultCount: this.state.maxResultCount,
			skipCount: this.state.skipCount,
			keyword: keywordString,
			sorting: sortString,
			searchColumn: searchColumnString,
			bunkeringDateStart: this.state.activeDateFilters.bunkeringDateStart || "",
			bunkeringDateEnd: this.state.activeDateFilters.bunkeringDateEnd || "",
		});

		this.setState({ loading: false });
	}

	handleTableChange = (
		pagination: TablePaginationConfig,
		_: Partial<Record<keyof GetBunkeringNotesOutput, string[]>>,
		sorter:
			| SorterResult<GetBunkeringNotesOutput>
			| SorterResult<GetBunkeringNotesOutput>[],
	) => {
		const sorters = utils.getSorters(sorter);

		this.setState(
			{
				skipCount: ((pagination.current ?? 1) - 1) * this.state.maxResultCount,
				maxResultCount: pagination.pageSize ?? 10,
				sorters: sorters,
			},
			async () => await this.getAll(),
		);
	};

	async fetchFilters(property: string) {
		this.setState({ fetchingFilters: true });
		const [searchColumnString, keywordString] = utils.getFilterStrings([
			...this.state.filters,
		]);

		try {
			await this.props.bunkeringNotesStore.getFilters(
				{
					keyword: keywordString,
					searchColumn: searchColumnString,
				},
				property,
			);
		} catch {
			Modal.error({
				title: "Error during fetching",
				content: (
					<div>
						<p>Failed to fetch filters for column</p>
					</div>
				),
			});
		}

		this.setState({ fetchingFilters: false });
	}

	getColumnSearchProps = (
		dataIndex: string,
		title: string,
	): ColumnProps<GetBunkeringNotesOutput> => ({
		filterDropdown: (props: FilterDropdownProps) => (
			<div>
				{props.visible && (
					<FilterSelect
						{...props}
						loading={this.state.fetchingFilters}
						handleFilter={(value) => this.handleFilter(value, dataIndex)}
						title={title}
						value={
							this.state.filters.find((x) => x.column === dataIndex)?.value
						}
						options={this.props.bunkeringNotesStore.filters?.map((x) => {
							return { key: x, value: x };
						})}
					/>
				)}
			</div>
		),
		onFilterDropdownVisibleChange: (v) => {
			if (v) this.fetchFilters(dataIndex);
		},
		filterIcon: () =>
			renderFilterIcon(
				this.state.filters.findIndex((x) => x.column === dataIndex) >= 0,
			),
		sortOrder: this.state.sorters.find((x) => x.columnKey === dataIndex)?.order,
	});

	Modal = () => {
		this.setState({
			modalVisible: !this.state.modalVisible,
		});
	};

	private handleFilter(value: string, column: string) {
		let currentFilters = [...this.state.filters];
		currentFilters = currentFilters.filter((f) => f.column !== column);

		if (value) {
			currentFilters.push({ value, column });
		}

		this.setState(
			{ filters: currentFilters, tempSearchValue: "" },
			async () => {
				await this.getAll();
			},
		);
	}

	async createOrupdateModalOpen(entityDto: EntityDto) {
		if (entityDto.id === 0) {
			await this.props.bunkeringNotesStore.createBunkeringNotes();
		} else {
			await this.props.bunkeringNotesStore.get(entityDto);
		}

		this.setState({ id: entityDto.id });
		this.Modal();

		if (!this.formRef) return;

		this.formRef.setFieldsValue({
			...this.props.bunkeringNotesStore.editBunkeringNotes,
			date: moment(this.props.bunkeringNotesStore.editBunkeringNotes.date),
			tempFileUrls: {
				fileList:
					this.props.bunkeringNotesStore.editBunkeringNotes.fileUrls.map(
						(x, i) => {
							return {
								uid: `${i}`,
								type: "",
								size: 0,
								name: x.name,
								url: x.url,
							};
						},
					),
			},
		});
	}

	delete(input: EntityDto) {
		const self = this;
		confirm({
			title: "Do you want to delete these items?",
			onOk() {
				self.props.bunkeringNotesStore.delete(input);
			},
			onCancel() {},
		});
	}
	// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
	saveFormRefEdit = (formRefEdit: any) => {
		if (!formRefEdit) return;
		this.formRef = formRefEdit.props.form;
	};

	setModalVisibleFalse = () => {
		this.setState({ modalVisible: false, okButtonDisabled: false });
	};

	showPdfModal = (record: GetBunkeringNotesOutput) => {
		this.setState({
			isPdfModalVisible: true,
			selectedPdfRecord: record,
		});
	};

	hidePdfModal = () => {
		this.setState({
			isPdfModalVisible: false,
			selectedPdfRecord: null,
			pageNumber: 1,
		});
	};

	handleCreate = () => {
		if (this.state.okButtonDisabled === false) {
			const form = this.formRef;
			if (!form) return;
			// biome-ignore lint/suspicious/noExplicitAny: Poor type implementation from library
			form.validateFields(async (err: any, values: any) => {
				if (err) {
					this.setState({ okButtonDisabled: false });
					return;
				}
				this.setState({ okButtonDisabled: true });
				try {
					if (values.portOfBunkering.id === 0) {
						values.portName =
							this.props.bunkeringNotesStore.editBunkeringNotes.portName;
						values.portOfBunkering = null;
					}

					if (this.state.id === 0) {
						await this.props.bunkeringNotesStore.create(values);
					} else {
						const fileUrls = values.tempFileUrls
							? values.tempFileUrls.fileList.map((x: { url: string }) => x.url)
							: this.props.bunkeringNotesStore.editBunkeringNotes.fileUrls.map(
									(x) => x.url,
								);

						await this.props.bunkeringNotesStore.update({
							id: this.state.id,
							...values,
							fileUrls: fileUrls,
						});
					}
					await this.getAll();
					form.resetFields();
					this.setModalVisibleFalse();
				} catch (ex) {
				} finally {
					this.setState({ okButtonDisabled: false });
				}
			});
		}
	};

	public render() {
		const { bunkeringNotes } = this.props.bunkeringNotesStore;
		const paginationOptions = getTablePaginationOptions(
			bunkeringNotes?.totalCount,
		);

		const { activeDateFilters } = this.state;
		const columns: ColumnProps<GetBunkeringNotesOutput>[] = [
			{
				title: L("Ship Name"),
				dataIndex: ["ship", "shipName"],
				key: "ship.shipName",
				width: 250,
				sorter: true,
				...this.getColumnSearchProps("ship.shipName", L("Ship Name")),
			},
			{
				title: L("IMO"),
				dataIndex: ["ship", "imoNumber"],
				key: "ship.imoNumber",
				width: 250,
				sorter: true,
				...this.getColumnSearchProps("ship.imoNumber", L("IMO")),
			},
			{
				title: L("Bunkering Date"),
				dataIndex: "date",
				key: "date",
				width: 250,
				render: (text) => renderDate(text, false),
				sorter: true,
				filterDropdown: ({ confirm, clearFilters }: FilterDropdownProps) => (
					<div style={{ padding: 8 }}>
						<DatePicker.RangePicker
							style={{ marginBottom: 8, width: 200 }}
							value={
								activeDateFilters.bunkeringDateStart &&
								activeDateFilters.bunkeringDateEnd
									? [
											moment(activeDateFilters.bunkeringDateStart),
											moment(activeDateFilters.bunkeringDateEnd),
										]
									: null
							}
							onChange={(dates) => {
								if (dates && dates.length === 2 && dates[0] && dates[1]) {
									const start = dates[0];
									const end = dates[1];
									this.setState(
										(prevState) => ({
											activeDateFilters: {
												...prevState.activeDateFilters,
												bunkeringDateStart: start.toISOString(),
												bunkeringDateEnd: end.toISOString(),
											},
										}),
										() => {
											confirm?.();
											this.getAll();
										},
									);
								} else {
									this.setState(
										(prevState) => ({
											activeDateFilters: {
												...prevState.activeDateFilters,
												bunkeringDateStart: undefined,
												bunkeringDateEnd: undefined,
											},
										}),
										() => {
											clearFilters?.();
											this.getAll();
										},
									);
								}
							}}
						/>
					</div>
				),
				filterIcon: () =>
					renderFilterIcon(
						activeDateFilters.bunkeringDateStart !== undefined &&
							activeDateFilters.bunkeringDateEnd !== undefined,
					),
			},
			{
				title: L("Delivery Location"),
				dataIndex: ["portName"],
				key: "portOfBunkering.name",
				width: 250,
				sorter: true,
				...this.getColumnSearchProps("portName", L("Delivery Location")),
			},
			{
				title: L("Supplier"),
				dataIndex: "supplier",
				key: "supplier",
				width: 250,
				sorter: true,
				...this.getColumnSearchProps("supplier", L("Supplier")),
			},
			{
				title: L("Fuel Type Loaded"),
				dataIndex: ["fuelTypeLoaded", "type"],
				key: "fuelTypeLoaded.type",
				width: 250,
				sorter: true,
				...this.getColumnSearchProps("fuelTypeLoaded", L("Fuel Type Loaded")),
			},
			{
				title: L("Quantity Loaded (MT)"),
				dataIndex: "quantityLoaded",
				key: "quantityLoaded",
				width: 250,
				sorter: true,
				render: (_, record) =>
					Number.parseFloat(record.quantityLoaded).toFixed(3),
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "quantityLoaded",
				)?.order,
			},
			{
				title: L("Density (Kg/m3) @ 15 Deg C"),
				dataIndex: "density",
				key: "density",
				width: 250,
				sorter: true,
				render: (_, record) => Number.parseFloat(record.density).toFixed(1),
				sortOrder: this.state.sorters.find((x) => x.columnKey === "density")
					?.order,
			},
			{
				title: L("Viscosity (mm2/s cSt, 40/50°C"),
				dataIndex: "viscosity",
				key: "viscosity",
				width: 250,
				sorter: true,
				sortOrder: this.state.sorters.find((x) => x.columnKey === "viscosity")
					?.order,
			},
			{
				title: L("Creation Time"),
				dataIndex: "creationTime",
				key: "creationTime",
				width: 200,
				render: (text) => renderDate(text, false),
				sorter: true,
				sortOrder: this.state.sorters.find(
					(x) => x.columnKey === "creationTime",
				)?.order,
			},
			{
				title: L("Documents"),
				width: 200,
				render: (_: string, record: GetBunkeringNotesOutput) => (
					<div>
						<Button type="primary" onClick={() => this.showPdfModal(record)}>
							{L("Show Documents")}
						</Button>
					</div>
				),
			},
			{
				title: L("Actions"),
				width: 150,
				fixed: "right" as const,
				render: (_: string, item) => (
					<div>
						<Dropdown
							trigger={["click"]}
							overlay={
								<Menu>
									{isGranted("Pages.BunkeringNotes-Edit") && (
										<MenuItem
											onClick={() =>
												this.createOrupdateModalOpen({ id: item.id })
											}
										>
											{L("Edit")}
										</MenuItem>
									)}
									{isGranted("Pages.BunkeringNotes-Delete") && (
										<MenuItem onClick={() => this.delete({ id: item.id })}>
											{L("Delete")}
										</MenuItem>
									)}
								</Menu>
							}
							placement="bottomLeft"
						>
							<Button type="primary" icon={<SettingOutlined />}>
								{L("Actions")}
							</Button>
						</Dropdown>
					</div>
				),
			},
		];

		const hasNoActions =
			!isGranted("Pages.BunkeringNotes-Edit") &&
			!isGranted("Pages.BunkeringNotes-Delete");

		if (hasNoActions) {
			columns.pop();
		}

		return (
			<Card>
				<Row style={{ marginTop: 20 }}>
					<Col
						xs={{ span: 24, offset: 0 }}
						sm={{ span: 24, offset: 0 }}
						md={{ span: 24, offset: 0 }}
						lg={{ span: 24, offset: 0 }}
						xl={{ span: 24, offset: 0 }}
						xxl={{ span: 24, offset: 0 }}
					>
						<Table
							rowKey={(record: GetBunkeringNotesOutput) => record.id.toString()}
							bordered={true}
							columns={columns}
							pagination={paginationOptions}
							loading={this.state.loading}
							dataSource={bunkeringNotes?.items}
							onChange={this.handleTableChange}
							scroll={{ y: 850 }}
						/>
					</Col>
				</Row>
				<Modal
					title="Document Viewer"
					visible={this.state.isPdfModalVisible}
					onCancel={this.hidePdfModal}
					footer={null}
					bodyStyle={{
						display: "flex",
						flexDirection: "column",
						gap: "10px",
						alignItems: "center",
					}}
				>
					<div className="pdfContainer" id="pdfContainer">
						{this.state.selectedPdfRecord?.fileUrls &&
						this.state.selectedPdfRecord?.fileUrls.length > 0 ? (
							this.state.selectedPdfRecord.fileUrls[
								this.state.pageNumber - 1
							].url
								.split("?")[0]
								.endsWith(".pdf") ? (
								<Document
									file={
										this.state.selectedPdfRecord?.fileUrls[
											this.state.pageNumber - 1
										].url
									}
								>
									<Page
										pageNumber={1}
										height={
											document.getElementById("pdfContainer")?.clientHeight ||
											500
										}
										className={"canvasStyle"}
										renderInteractiveForms
										renderAnnotationLayer
									/>
								</Document>
							) : (
								<img
									src={
										this.state.selectedPdfRecord?.fileUrls[
											this.state.pageNumber - 1
										].url
									}
									style={{ width: "100%", height: "auto" }}
									alt="Document"
								/>
							)
						) : (
							<span>No documents found</span>
						)}
					</div>
					{this.state.selectedPdfRecord?.fileUrls.length ? (
						<Pagination
							size="small"
							current={this.state.pageNumber}
							onChange={(page) => this.setState({ pageNumber: page })}
							total={this.state.selectedPdfRecord?.fileUrls.length}
							pageSize={1}
						/>
					) : null}
				</Modal>
				<UpdateBunkeringNotes
					defaultFiles={
						this.props.bunkeringNotesStore.editBunkeringNotes
							? this.props.bunkeringNotesStore.editBunkeringNotes.fileUrls
							: []
					}
					portStore={this.props.portStore}
					shipStore={this.props.shipStore}
					fuelTypeStore={this.props.fuelTypeStore}
					bunkeringNotesStore={this.props.bunkeringNotesStore}
					visible={this.state.modalVisible}
					wrappedComponentRef={this.saveFormRefEdit}
					onCancel={this.setModalVisibleFalse}
					modalType={ModalType.edit}
					onCreate={this.handleCreate}
					okButtonDisabled={this.state.okButtonDisabled}
					roles={this.props.bunkeringNotesStore.roles}
				/>
				<Chat />
			</Card>
		);
	}
}
export default BunkeringNotes;
