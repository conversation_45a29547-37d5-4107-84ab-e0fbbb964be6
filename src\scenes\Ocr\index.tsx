import * as React from 'react';
import './index.less';
import { Tabs } from 'antd';
import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Chat from '../../components/Chat';
import { L } from '../../lib/abpUtility';
import { OcrDto } from '../../services/ocr/dTo/ocrDto';
import OcrStore from '../../stores/ocrStore';
import Stores from '../../stores/storeIdentifier';
import VerifiedTable from './components/VerifiedTable';
import AfterOcrTable from './components/afterOcrTable';
import BeforeOcrTable from './components/beforeOcrTable';
import DeniedTable from './components/deniedTable';
import MovedTable from './components/movedTable';
import CocpitTable from './components/cocpitTable';
import OverflowLoading from '../../components/OverflowLoading';
import { CocpitDto } from '../../services/ocr/dTo/cocpitDto';

const { TabPane } = Tabs;

export interface IOcrProps {
  ocrStore: OcrStore;
}

export interface IOcrState {
  modalVisible: boolean;
  ocrId: number;
  notesTabStatus: number;
  cockpitTabStatus: number;
  selectedRow: OcrDto | null;
  isLoading: boolean;
}

@inject(Stores.OcrStore)
@observer
class Ocr extends AppComponentBase<IOcrProps, IOcrState> {
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  formRef: any;

  state = {
    modalVisible: false,
    ocrId: 0,

    selectedRow: null as OcrDto | null,
    notesTabStatus: 0,
    cockpitTabStatus: 0,
    isLoading: true,
  };

  async fetchAll() {
    this.setState({
      isLoading: true,
    });
    await this.props.ocrStore.getAllByStatus(
      {
        status: this.state.notesTabStatus,
      },
      { status: this.state.cockpitTabStatus }
    );
    this.setState({
      isLoading: false,
    });
  }

  async componentDidUpdate(_prevProps: Readonly<IOcrProps>, prevState: Readonly<IOcrState>): Promise<void> {
    if (prevState.notesTabStatus !== this.state.notesTabStatus || prevState.cockpitTabStatus !== this.state.cockpitTabStatus) {
      await this.fetchAll();
    }
  }

  async componentDidMount() {
    await this.fetchAll();
  }

  analyzeFile = async (ocrStore: OcrDto) => {
    await this.props.ocrStore?.analyzeFile(ocrStore);
    await this.fetchAll();
  };

  analyzeCocpitFile = async (/*ocrStore: OcrDto*/ fileUrl: string) => {
    this.setState({
      isLoading: true,
    });
    var fields = await this.props.ocrStore?.analyzeCocpitFile(fileUrl);
    this.setState({
      isLoading: false,
    });
    return fields;
    //await this.fetchAll();
  };

  exportFile = async (cocpitDto: CocpitDto) => {
    this.setState({
      isLoading: true,
    });
    await this.props.ocrStore.exportFile(cocpitDto);
    this.setState({
      isLoading: false,
    });
  };

  verifyFile = async (files: OcrDto[]) => {
    await this.props.ocrStore?.verifyFile(files);
    await this.fetchAll();
  };

  denyFile = async (ocrStore: OcrDto) => {
    await this.props.ocrStore?.denyFile(ocrStore);
    await this.fetchAll();
  };

  moveFile = async (ocrStore: OcrDto) => {
    await this.props.ocrStore?.moveFile(ocrStore);
    await this.fetchAll();
  };

  Modal = () => {
    this.setState({
      modalVisible: !this.state.modalVisible,
    });
  };

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  saveFormRef = (formRef: any) => {
    this.formRef = formRef;
  };

  public render() {
    return (
      <>
        <Tabs
          defaultActiveKey="1"
          onTabClick={(key: string) => {
            this.setState({
              notesTabStatus: Number.parseInt(key) - 1,
            });
          }}
        >
          <TabPane tab={L('BeforeOCR')} key="1">
            <BeforeOcrTable
              ocrStore={this.props.ocrStore}
              ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []}
              analyzeFile={this.analyzeFile}
              denyFile={this.denyFile}
              isCocpit={false}
            />
          </TabPane>
          <TabPane tab={L('AfterOCR')} key="2">
            <AfterOcrTable
              selectedRow={this.state.selectedRow}
              ocrStore={this.props.ocrStore}
              ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []}
              verifyFile={this.verifyFile}
              isCocpit={false}
            />
          </TabPane>
          <TabPane tab={L('Verified')} key="3">
            <VerifiedTable ocrStore={this.props.ocrStore} ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []} moveFile={this.moveFile} />
          </TabPane>
          <TabPane tab={L('Denied')} key="4">
            <DeniedTable ocrStore={this.props.ocrStore} ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []} />
          </TabPane>
          <TabPane tab={L('Accepted')} key="5">
            <MovedTable ocrStore={this.props.ocrStore} ocr={this.props.ocrStore.ocr ? this.props.ocrStore.ocr : []} />
          </TabPane>
        </Tabs>
        <Chat />
        <h2>{L('Screen/Document capture')}</h2>
        {/* <Tabs
          defaultActiveKey="1"
          onTabClick={(key: string) => {
            this.setState({
              cockpitTabStatus: Number.parseInt(key) - 1,
            });
          }}
        >
          <TabPane tab={L('BeforeOCR')} key="1">
            <BeforeOcrTable
              ocrStore={this.props.ocrStore}
              ocr={this.props.ocrStore.cocpitOcr ? this.props.ocrStore.cocpitOcr : []}
              analyzeFile={this.analyzeCocpitFile}
              denyFile={this.denyFile}
              isCocpit={true}
            />
          </TabPane>
          <TabPane tab={L('AfterOCR')} key="2">
            <AfterOcrTable
              selectedRow={this.state.selectedRow}
              ocrStore={this.props.ocrStore}
              ocr={this.props.ocrStore.cocpitOcr ? this.props.ocrStore.cocpitOcr : []}
              verifyFile={this.verifyFile}
              isCocpit={true}
            />
          </TabPane>
          <TabPane tab={L('Verified')} key="3">
            <VerifiedTable
              ocrStore={this.props.ocrStore}
              ocr={this.props.ocrStore.cocpitOcr ? this.props.ocrStore.cocpitOcr : []}
              moveFile={this.moveFile}
            />
          </TabPane>
          <TabPane tab={L('Denied')} key="4">
            <DeniedTable ocrStore={this.props.ocrStore} ocr={this.props.ocrStore.cocpitOcr ? this.props.ocrStore.cocpitOcr : []} />
          </TabPane>
          <TabPane tab={L('Accepted')} key="5">
            <MovedTable ocrStore={this.props.ocrStore} ocr={this.props.ocrStore.cocpitOcr ? this.props.ocrStore.cocpitOcr : []} />
          </TabPane>
        </Tabs> */}
        <CocpitTable
          ocrStore={this.props.ocrStore}
          ocr={this.props.ocrStore.cocpitOcr ? this.props.ocrStore.cocpitOcr : []}
          handleAnalyzeFile={(fileUrl: string) => this.analyzeCocpitFile(fileUrl)}
          exportFile={(cocpitDto: CocpitDto) => this.exportFile(cocpitDto)}
        />
        {this.state.isLoading && <OverflowLoading />}
      </>
    );
  }
}

export default Ocr;
