import { Button, Table } from 'antd';
import { inject, observer } from 'mobx-react';
import moment from 'moment';
import React from 'react';
import { L } from '../../../lib/abpUtility';
import fileService from '../../../services/azure/fileService';
import { EntityDto } from '../../../services/dto/entityDto';
import { OcrDto } from '../../../services/ocr/dTo/ocrDto';
import OcrStore from '../../../stores/ocrStore';
import Stores from '../../../stores/storeIdentifier';
import { renderDate } from '../../renderUtils';
import { TableRowSelection } from 'antd/lib/table/interface';
import { CocpitDto } from '../../../services/ocr/dTo/cocpitDto';
import CocpitValueModal from './cocpitModal';

// const { Option } = Select;

interface ICocpitTableProps {
  ocrStore: OcrStore;
  ocr: any;
  handleAnalyzeFile: (fielUrl: string) => Promise<CocpitDto>;
  exportFile: (cocpitDto: CocpitDto) => Promise<void>;
}

interface SelectedItem {
  id: number;
  fileUrl: string;
}

@inject(Stores.OcrStore)
@observer
class CocpitTable extends React.Component<ICocpitTableProps> {
  ocrStore: OcrStore = this.props.ocrStore;
  fileUploadInputRef: any;
  selectedFilesForUpload: any = {
    names: [] as string[],
    scrs: [] as string[],
  };

  state = {
    isModalVisible: false,
    selectedFiles: [],
    selectedRows: {
      items: [] as SelectedItem[],
    },
    status: 0,
    extractedFields: null as CocpitDto | null,
    overrideFields: {} as { [key: string]: string },
  };

  constructor(props: any) {
    super(props);
    this.fileUploadInputRef = React.createRef();
    this.state = {
      isModalVisible: false,
      selectedFiles: [],
      selectedRows: {
        items: [],
      },
      status: 0,
      extractedFields: null,
      overrideFields: {},
    };
  }

  showModal = () => {
    this.setState({ isModalVisible: true });
  };

  handleOk = () => {
    this.setState({ isModalVisible: false, extractedFields: null, overrideFields: {} });
  };

  handleCancel = () => {
    this.setState({ isModalVisible: false, extractedFields: null, overrideFields: {} });
  };

  exportFile = () => {
    var overrideFields = this.state.overrideFields;
    var extractedFields = this.state.extractedFields;
    if (extractedFields) {
      const updatedControlBoard = extractedFields.data.map((item) => {
        if (overrideFields.hasOwnProperty(item.id)) {
          return {
            ...item,
            Value: overrideFields[item.id],
          };
        }
        return item;
      });

      const updatedExtractedFields: CocpitDto = {
        ...extractedFields,
        data: updatedControlBoard,
      };
      if (!updatedExtractedFields) return;
      this.props.exportFile(updatedExtractedFields);
    }
  };

  handleFileChange = (event: any) => {
    this.setState({ selectedFiles: event.target.files });
  };

  triggerUpload = () => {
    this.fileUploadInputRef.current.click();
  };

  handleAnalyzeFile = async () => {
    if (this.state.selectedRows.items.length === 0) return;
    const parsedFields = await this.props.handleAnalyzeFile(this.state.selectedRows.items[0].fileUrl);
    this.setState({ extractedFields: parsedFields, isModalVisible: true });
  };

  handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    e.persist();
    const selectedFiles = this.fileUploadInputRef.current.files;
    if (!selectedFiles || selectedFiles.length === 0) {
      return;
    }

    this.selectedFilesForUpload.names = [];
    this.selectedFilesForUpload.srcs = [];

    const tempOcrUrls: Pick<OcrDto, 'fileName' | 'fileUrl'>[] = [];

    for (let i = 0; i < selectedFiles.length; i++) {
      const currentFile = selectedFiles[i];
      const result = await fileService.uploadFileOCR(currentFile);

      tempOcrUrls.push({
        fileName: currentFile.name,
        fileUrl: result,
      });
    }

    this.setState({ isModalVisible: false, extractedFields: null });
    await this.props.ocrStore.createMany(tempOcrUrls);
    this.resetFileInput();
    await this.props.ocrStore.getByStatus(
      {
        status: this.state.status,
      },
      true
    );
  };
  resetFileInput = () => {
    if (this.fileUploadInputRef?.current) {
      this.fileUploadInputRef.current.value = '';
    }
  };

  public render() {
    const columns = [
      {
        title: L('CreationTime'),
        dataIndex: 'creationTime',
        key: 'creationTime',
        width: 150,
        sorter: (a: OcrDto, b: OcrDto) => moment(a.creationTime).diff(moment(b.creationTime)),
        render: (text: string, rekord: OcrDto, index: number) => renderDate(text),
      },
      {
        title: L('LastModificationTime'),
        dataIndex: 'lastModificationTime',
        key: 'lastModificationTime',
        width: 150,
        sorter: (a: OcrDto, b: OcrDto) => moment(a.lastModificationTime).diff(moment(b.lastModificationTime)),
        render: (text: string, rekord: OcrDto, index: number) => renderDate(text),
      },
      {
        title: L('FileName'),
        dataIndex: 'fileName',
        key: 'fileName',
        width: 150,
      },
      {
        title: L('Status'),
        dataIndex: 'status',
        key: 'status',
        width: 150,
      },
    ];

    const { selectedRows } = this.state;
    const rowSelection: TableRowSelection<OcrDto> = {
      onChange: (selectedRowKeys, selectedRows: OcrDto[]) => {
        const selectedRowsItems: EntityDto[] = selectedRows.map((x) => {
          return {
            id: x.id,
            fileUrl: x.fileUrl,
          };
        });
        this.setState({
          selectedRows: {
            items: selectedRowsItems,
          },
        });
      },
    };

    const hasSelected = selectedRows.items.length > 0;

    // let documentTypes = [] ? [] : [];

    return (
      <>
        <div>
          <div className="ocr-header__buttons">
            <Button type="primary" onClick={this.triggerUpload} disabled={false}>
              {L('Add File')}
            </Button>

            <Button type="primary" onClick={() => this.handleAnalyzeFile()} disabled={!hasSelected}>
              {L('Analyze File')}
            </Button>
          </div>
          <Table
            bordered={true}
            rowKey={(record: OcrDto) => record.id.toString()}
            columns={columns}
            dataSource={this.props.ocr ? this.props.ocr : []}
            rowSelection={rowSelection}
          />

          <input
            type="file"
            title={L('Add File')}
            accept={'.jpg, .jpeg, .png'}
            ref={this.fileUploadInputRef}
            multiple
            onChange={async (e) => await this.handleFileUpload(e)}
            style={{ display: 'none' }}
          />

          <CocpitValueModal
            visible={this.state.isModalVisible && this.state.extractedFields !== null}
            onOk={this.handleOk}
            onCancel={this.handleCancel}
            exportFile={this.exportFile}
            fileUrl={
              this.state.selectedRows.items.length > 0 && this.state.selectedRows.items[0].fileUrl ? this.state.selectedRows.items[0].fileUrl : ''
            }
            jsonContent={this.state.extractedFields}
            overrideFields={this.state.overrideFields}
            onFieldChange={(key, value) => {
              const localFields = { ...this.state.overrideFields };
              localFields[key] = value;
              this.setState({ overrideFields: localFields });
            }}
          />
        </div>
      </>
    );
  }
}

export default CocpitTable;
