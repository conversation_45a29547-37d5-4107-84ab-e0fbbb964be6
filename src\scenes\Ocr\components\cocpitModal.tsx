import React from 'react';
import { Modal, Button, Input, Form } from 'antd';
import { L } from '../../../lib/abpUtility';
import { CocpitDto } from '../../../services/ocr/dTo/cocpitDto';

interface CocpitValueModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  exportFile: () => void;
  // onVerify: () => void;
  fileUrl: string;
  jsonContent: CocpitDto | null;
  overrideFields: Record<string, string>;
  onFieldChange: (key: string, value: string) => void;
}

export const CocpitValueModal: React.FC<CocpitValueModalProps> = ({
  visible,
  onOk,
  onCancel,
  exportFile,
  // onVerify,
  fileUrl,
  jsonContent,
  overrideFields,
  onFieldChange,
}) => {
  if (!jsonContent) return <div />;
  return (
    <Modal
      title={`${jsonContent.systemName} ${jsonContent.utc}`}
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      width="90%"
      style={{ top: '5vh' }}
      footer={
        <div style={{ display: 'flex', justifyContent: 'end' }}>
          {/*<Button key="verifyFile" type="primary" style={{ marginRight: 8 }} onClick={onVerify}>
            {L('VerifyFile')}
          </Button>*/}
          <div>
            <Button key="back" onClick={exportFile} style={{ marginRight: 8 }}>
              {L('Export file')}
            </Button>
            <Button key="back" onClick={onCancel} style={{ marginRight: 8 }}>
              {L('Cancel')}
            </Button>
            <Button key="submit" type="primary" onClick={onOk}>
              {L('OK')}
            </Button>
          </div>
        </div>
      }
    >
      <div className="dataContainer">
        <div className="pdfContainer" id="pdfContainer">
          <img src={fileUrl} alt="Document" style={{ width: '100%', height: 'auto' }} />
        </div>

        <div className="columnDataContainer">
          <h2>{L('PREDICT')}</h2>
          {jsonContent.data.map((item, index) => (
            <div key={index}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <strong>{item.description}:</strong> {item.value}
                <Form.Item>
                  <Input value={overrideFields?.[item.id] ?? item.value} onChange={(e) => onFieldChange(item.id, e.target.value)} />
                </Form.Item>
              </div>
              <hr />
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};
export default CocpitValueModal;
